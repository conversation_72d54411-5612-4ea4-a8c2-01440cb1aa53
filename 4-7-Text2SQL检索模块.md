# Text2SQL检索模块 (Text-to-SQL Retrieval)

## 模块概述

Text2SQL检索模块是RAG系统中专门处理结构化数据查询的核心组件，负责将自然语言查询转换为SQL语句，并对结构化数据（表格数据、数据库表等）进行智能查询。该模块与传统向量检索形成互补，专门解决传统向量检索无法有效处理的结构化数据场景。

## 核心功能

### 1. 自然语言到SQL转换
- **查询解析**：理解自然语言查询的语义和意图
- **SQL生成**：将自然语言转换为标准SQL语句
- **语法验证**：确保生成的SQL语句语法正确
- **优化建议**：提供SQL性能优化建议

### 2. 结构化数据Schema理解
- **表结构分析**：自动分析表结构、字段类型、约束关系
- **语义映射**：建立自然语言概念与数据库字段的映射关系
- **关系推理**：理解表间关系和外键约束
- **数据类型推断**：智能推断查询条件的数据类型

### 3. 多数据源支持
- **CSV文件**：支持CSV格式的表格数据查询
- **Excel文件**：支持Excel工作表的结构化查询
- **关系数据库**：支持MySQL、PostgreSQL、SQLite等数据库
- **NoSQL数据库**：支持MongoDB等文档数据库的结构化查询

## 技术架构

### 整体架构图

```mermaid
graph TD
    A[自然语言查询] --> B[查询预处理]
    B --> C[意图识别与分类]
    C --> D[Schema理解与映射]
    D --> E[SQL生成引擎]
    E --> F[SQL验证与优化]
    F --> G[查询执行引擎]
    G --> H[结果后处理]
    H --> I[结构化结果输出]
    
    J[数据源管理] --> K[Schema缓存]
    K --> D
    J --> L[连接池管理]
    L --> G
    
    M[SpringAI集成] --> C
    M --> E
    
    N[监控与日志] --> O[查询性能监控]
    N --> P[错误日志记录]
    
    style A fill:#e1f5fe
    style I fill:#c8e6c9
    style M fill:#fff3e0
```

### 核心组件

#### 1. 查询预处理器
```java
@Component
public class QueryPreprocessor {
    
    /**
     * 预处理自然语言查询
     */
    public ProcessedQuery preprocess(String naturalQuery) {
        return ProcessedQuery.builder()
            .originalQuery(naturalQuery)
            .cleanedQuery(cleanQuery(naturalQuery))
            .extractedEntities(extractEntities(naturalQuery))
            .queryType(classifyQueryType(naturalQuery))
            .build();
    }
    
    private String cleanQuery(String query) {
        // 清理查询文本，去除噪音
        return query.trim()
            .replaceAll("\\s+", " ")
            .toLowerCase();
    }
    
    private List<String> extractEntities(String query) {
        // 提取查询中的实体（表名、字段名等）
        // 使用NER模型或规则匹配
        return entityExtractor.extract(query);
    }
    
    private QueryType classifyQueryType(String query) {
        // 分类查询类型：SELECT、COUNT、SUM、GROUP BY等
        if (query.contains("多少") || query.contains("数量")) {
            return QueryType.COUNT;
        } else if (query.contains("总和") || query.contains("合计")) {
            return QueryType.SUM;
        } else if (query.contains("平均")) {
            return QueryType.AVERAGE;
        }
        return QueryType.SELECT;
    }
}
```

#### 2. Schema理解引擎
```java
@Component
public class SchemaUnderstandingEngine {
    
    @Autowired
    private DataSourceManager dataSourceManager;
    
    @Autowired
    private SchemaCache schemaCache;
    
    /**
     * 理解数据源Schema并建立映射
     */
    public SchemaMapping buildSchemaMapping(String dataSourceId, ProcessedQuery query) {
        Schema schema = getOrCacheSchema(dataSourceId);
        return SchemaMapping.builder()
            .schema(schema)
            .fieldMappings(mapQueryToFields(query, schema))
            .tableMappings(mapQueryToTables(query, schema))
            .relationMappings(mapQueryToRelations(query, schema))
            .build();
    }
    
    private Schema getOrCacheSchema(String dataSourceId) {
        return schemaCache.computeIfAbsent(dataSourceId, id -> {
            DataSource dataSource = dataSourceManager.getDataSource(id);
            return schemaAnalyzer.analyzeSchema(dataSource);
        });
    }
    
    private Map<String, String> mapQueryToFields(ProcessedQuery query, Schema schema) {
        Map<String, String> mappings = new HashMap<>();
        
        // 使用语义相似度匹配字段
        for (String entity : query.getExtractedEntities()) {
            String bestMatch = findBestFieldMatch(entity, schema);
            if (bestMatch != null) {
                mappings.put(entity, bestMatch);
            }
        }
        
        return mappings;
    }
    
    private String findBestFieldMatch(String entity, Schema schema) {
        // 使用向量相似度或编辑距离找到最佳匹配字段
        return schema.getFields().stream()
            .max(Comparator.comparing(field -> 
                semanticSimilarity.calculate(entity, field.getName())))
            .map(Field::getName)
            .orElse(null);
    }
}
```

#### 3. SQL生成引擎
```java
@Component
public class SQLGenerationEngine {
    
    @Autowired
    private SpringAIService springAIService;
    
    /**
     * 生成SQL语句
     */
    public GeneratedSQL generateSQL(ProcessedQuery query, SchemaMapping schemaMapping) {
        // 构建提示词
        String prompt = buildSQLPrompt(query, schemaMapping);
        
        // 使用SpringAI生成SQL
        String generatedSQL = springAIService.generateSQL(prompt);
        
        // 验证和优化SQL
        ValidatedSQL validatedSQL = validateAndOptimizeSQL(generatedSQL, schemaMapping.getSchema());
        
        return GeneratedSQL.builder()
            .originalQuery(query.getOriginalQuery())
            .generatedSQL(validatedSQL.getSql())
            .confidence(validatedSQL.getConfidence())
            .optimizationSuggestions(validatedSQL.getOptimizations())
            .build();
    }
    
    private String buildSQLPrompt(ProcessedQuery query, SchemaMapping schemaMapping) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("将以下自然语言查询转换为SQL语句：\n");
        prompt.append("查询：").append(query.getOriginalQuery()).append("\n\n");
        
        // 添加Schema信息
        prompt.append("数据库Schema：\n");
        for (Table table : schemaMapping.getSchema().getTables()) {
            prompt.append("表名：").append(table.getName()).append("\n");
            prompt.append("字段：");
            table.getFields().forEach(field -> 
                prompt.append(field.getName()).append("(").append(field.getType()).append("), "));
            prompt.append("\n\n");
        }
        
        // 添加字段映射信息
        prompt.append("字段映射：\n");
        schemaMapping.getFieldMappings().forEach((key, value) -> 
            prompt.append(key).append(" -> ").append(value).append("\n"));
        
        prompt.append("\n请生成标准的SQL语句：");
        return prompt.toString();
    }
    
    private ValidatedSQL validateAndOptimizeSQL(String sql, Schema schema) {
        // SQL语法验证
        boolean isValid = sqlValidator.validate(sql);
        
        // 性能优化建议
        List<String> optimizations = sqlOptimizer.analyze(sql, schema);
        
        // 计算置信度
        double confidence = calculateConfidence(sql, schema);
        
        return ValidatedSQL.builder()
            .sql(sql)
            .isValid(isValid)
            .confidence(confidence)
            .optimizations(optimizations)
            .build();
    }
}
```

#### 4. 查询执行引擎
```java
@Component
public class QueryExecutionEngine {
    
    @Autowired
    private DataSourceManager dataSourceManager;
    
    @Autowired
    private ConnectionPoolManager connectionPoolManager;
    
    /**
     * 执行SQL查询
     */
    public QueryResult executeQuery(GeneratedSQL generatedSQL, String dataSourceId) {
        try {
            DataSource dataSource = dataSourceManager.getDataSource(dataSourceId);
            Connection connection = connectionPoolManager.getConnection(dataSource);
            
            // 执行查询
            PreparedStatement statement = connection.prepareStatement(generatedSQL.getGeneratedSQL());
            ResultSet resultSet = statement.executeQuery();
            
            // 转换结果
            List<Map<String, Object>> results = convertResultSet(resultSet);
            
            return QueryResult.builder()
                .originalQuery(generatedSQL.getOriginalQuery())
                .executedSQL(generatedSQL.getGeneratedSQL())
                .results(results)
                .executionTime(System.currentTimeMillis())
                .success(true)
                .build();
                
        } catch (SQLException e) {
            return QueryResult.builder()
                .originalQuery(generatedSQL.getOriginalQuery())
                .executedSQL(generatedSQL.getGeneratedSQL())
                .error(e.getMessage())
                .success(false)
                .build();
        }
    }
    
    private List<Map<String, Object>> convertResultSet(ResultSet resultSet) throws SQLException {
        List<Map<String, Object>> results = new ArrayList<>();
        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();
        
        while (resultSet.next()) {
            Map<String, Object> row = new HashMap<>();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                Object value = resultSet.getObject(i);
                row.put(columnName, value);
            }
            results.add(row);
        }
        
        return results;
    }
}
```

## SpringAI集成方案

### 1. SpringAI配置
```java
@Configuration
@EnableConfigurationProperties(Text2SQLProperties.class)
public class Text2SQLConfiguration {
    
    @Bean
    public ChatClient chatClient(ChatClient.Builder builder) {
        return builder
            .defaultSystem("你是一个专业的SQL生成助手，能够将自然语言查询转换为准确的SQL语句。")
            .build();
    }
    
    @Bean
    public SpringAIService springAIService(ChatClient chatClient) {
        return new SpringAIService(chatClient);
    }
}

@ConfigurationProperties(prefix = "text2sql.ai")
@Data
public class Text2SQLProperties {
    private String model = "gpt-4";
    private double temperature = 0.1;
    private int maxTokens = 1000;
    private boolean enableOptimization = true;
}
```

### 2. SpringAI服务实现
```java
@Service
public class SpringAIService {
    
    private final ChatClient chatClient;
    
    public SpringAIService(ChatClient chatClient) {
        this.chatClient = chatClient;
    }
    
    /**
     * 使用SpringAI生成SQL
     */
    public String generateSQL(String prompt) {
        return chatClient.prompt()
            .user(prompt)
            .call()
            .content();
    }
    
    /**
     * 优化SQL语句
     */
    public String optimizeSQL(String sql, String schema) {
        String optimizationPrompt = String.format(
            "请优化以下SQL语句的性能：\n" +
            "SQL: %s\n" +
            "Schema: %s\n" +
            "请提供优化后的SQL语句和优化说明：", 
            sql, schema
        );
        
        return chatClient.prompt()
            .user(optimizationPrompt)
            .call()
            .content();
    }
    
    /**
     * 解释SQL语句
     */
    public String explainSQL(String sql) {
        String explainPrompt = String.format(
            "请用自然语言解释以下SQL语句的功能：\n%s", sql
        );
        
        return chatClient.prompt()
            .user(explainPrompt)
            .call()
            .content();
    }
}
```

## 数据存储方案

### 1. 多数据源管理
```java
@Component
public class DataSourceManager {
    
    private final Map<String, DataSource> dataSources = new ConcurrentHashMap<>();
    
    /**
     * 注册CSV数据源
     */
    public void registerCSVDataSource(String id, String filePath) {
        CSVDataSource csvDataSource = new CSVDataSource(filePath);
        dataSources.put(id, csvDataSource);
    }
    
    /**
     * 注册Excel数据源
     */
    public void registerExcelDataSource(String id, String filePath, String sheetName) {
        ExcelDataSource excelDataSource = new ExcelDataSource(filePath, sheetName);
        dataSources.put(id, excelDataSource);
    }
    
    /**
     * 注册数据库数据源
     */
    public void registerDatabaseDataSource(String id, String jdbcUrl, String username, String password) {
        DatabaseDataSource dbDataSource = new DatabaseDataSource(jdbcUrl, username, password);
        dataSources.put(id, dbDataSource);
    }
    
    public DataSource getDataSource(String id) {
        return dataSources.get(id);
    }
}
```

### 2. 文件数据源实现
```java
public class CSVDataSource implements DataSource {
    
    private final String filePath;
    private Schema schema;
    
    public CSVDataSource(String filePath) {
        this.filePath = filePath;
        this.schema = analyzeCSVSchema();
    }
    
    private Schema analyzeCSVSchema() {
        try (CSVReader reader = new CSVReader(new FileReader(filePath))) {
            String[] headers = reader.readNext();
            String[] firstRow = reader.readNext();
            
            List<Field> fields = new ArrayList<>();
            for (int i = 0; i < headers.length; i++) {
                String fieldName = headers[i];
                String fieldType = inferDataType(firstRow[i]);
                fields.add(new Field(fieldName, fieldType));
            }
            
            return Schema.builder()
                .tables(List.of(Table.builder()
                    .name("csv_data")
                    .fields(fields)
                    .build()))
                .build();
                
        } catch (IOException e) {
            throw new RuntimeException("Failed to analyze CSV schema", e);
        }
    }
    
    private String inferDataType(String value) {
        if (value == null || value.isEmpty()) {
            return "VARCHAR";
        }
        
        try {
            Integer.parseInt(value);
            return "INTEGER";
        } catch (NumberFormatException e1) {
            try {
                Double.parseDouble(value);
                return "DOUBLE";
            } catch (NumberFormatException e2) {
                return "VARCHAR";
            }
        }
    }
    
    public List<Map<String, Object>> query(String sql) {
        // 使用H2内存数据库执行CSV查询
        return executeCSVQuery(sql);
    }
}
```

### 3. 缓存策略
```java
@Component
public class SchemaCache {
    
    private final Cache<String, Schema> schemaCache;
    private final Cache<String, List<Map<String, Object>>> queryCache;
    
    public SchemaCache() {
        this.schemaCache = Caffeine.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();
            
        this.queryCache = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build();
    }
    
    public Schema computeIfAbsent(String dataSourceId, Function<String, Schema> loader) {
        return schemaCache.get(dataSourceId, loader);
    }
    
    public List<Map<String, Object>> getCachedQuery(String queryKey) {
        return queryCache.getIfPresent(queryKey);
    }
    
    public void cacheQuery(String queryKey, List<Map<String, Object>> result) {
        queryCache.put(queryKey, result);
    }
}
```

## 配置参数

```yaml
text2sql:
  enabled: true
  
  # SpringAI配置
  ai:
    model: "gpt-4"
    temperature: 0.1
    max-tokens: 1000
    enable-optimization: true
    
  # 数据源配置
  datasources:
    default-timeout: 30s
    max-connections: 20
    connection-pool-size: 10
    
  # 缓存配置
  cache:
    schema-cache-size: 100
    schema-cache-ttl: 1h
    query-cache-size: 1000
    query-cache-ttl: 30m
    
  # 查询配置
  query:
    max-result-size: 1000
    enable-query-optimization: true
    enable-sql-validation: true
    confidence-threshold: 0.7
    
  # 监控配置
  monitoring:
    enable-performance-monitoring: true
    enable-query-logging: true
    slow-query-threshold: 5s
```

## 集成接口

### 1. Text2SQL检索接口
```java
@RestController
@RequestMapping("/api/text2sql")
public class Text2SQLController {
    
    @Autowired
    private Text2SQLRetrievalService text2sqlService;
    
    @PostMapping("/query")
    public ResponseEntity<Text2SQLResult> query(@RequestBody Text2SQLRequest request) {
        Text2SQLResult result = text2sqlService.processQuery(
            request.getNaturalQuery(),
            request.getDataSourceId()
        );
        return ResponseEntity.ok(result);
    }
    
    @PostMapping("/explain")
    public ResponseEntity<String> explainSQL(@RequestBody SQLExplainRequest request) {
        String explanation = text2sqlService.explainSQL(request.getSql());
        return ResponseEntity.ok(explanation);
    }
}
```

### 2. 与检索模块集成
```java
@Component
public class Text2SQLRetrievalService {
    
    @Autowired
    private QueryPreprocessor queryPreprocessor;
    
    @Autowired
    private SchemaUnderstandingEngine schemaEngine;
    
    @Autowired
    private SQLGenerationEngine sqlEngine;
    
    @Autowired
    private QueryExecutionEngine executionEngine;
    
    /**
     * 处理Text2SQL查询
     */
    public Text2SQLResult processQuery(String naturalQuery, String dataSourceId) {
        // 1. 查询预处理
        ProcessedQuery processedQuery = queryPreprocessor.preprocess(naturalQuery);
        
        // 2. Schema理解与映射
        SchemaMapping schemaMapping = schemaEngine.buildSchemaMapping(dataSourceId, processedQuery);
        
        // 3. SQL生成
        GeneratedSQL generatedSQL = sqlEngine.generateSQL(processedQuery, schemaMapping);
        
        // 4. 查询执行
        QueryResult queryResult = executionEngine.executeQuery(generatedSQL, dataSourceId);
        
        // 5. 结果封装
        return Text2SQLResult.builder()
            .originalQuery(naturalQuery)
            .generatedSQL(generatedSQL.getGeneratedSQL())
            .queryResult(queryResult)
            .confidence(generatedSQL.getConfidence())
            .executionTime(queryResult.getExecutionTime())
            .build();
    }
}
```

## 监控与优化

### 1. 性能监控
```java
@Component
public class Text2SQLMonitor {
    
    private final MeterRegistry meterRegistry;
    
    public Text2SQLMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }
    
    public void recordQueryExecution(String dataSourceId, long executionTime, boolean success) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("text2sql.query.execution")
            .tag("datasource", dataSourceId)
            .tag("success", String.valueOf(success))
            .register(meterRegistry));
            
        meterRegistry.counter("text2sql.query.count",
            "datasource", dataSourceId,
            "success", String.valueOf(success))
            .increment();
    }
    
    public void recordSQLGeneration(double confidence, long generationTime) {
        meterRegistry.gauge("text2sql.sql.confidence", confidence);
        meterRegistry.timer("text2sql.sql.generation").record(generationTime, TimeUnit.MILLISECONDS);
    }
}
```

### 2. 错误处理
```java
@Component
public class Text2SQLErrorHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(Text2SQLErrorHandler.class);
    
    public Text2SQLResult handleError(Exception e, String naturalQuery) {
        logger.error("Text2SQL processing failed for query: {}", naturalQuery, e);
        
        return Text2SQLResult.builder()
            .originalQuery(naturalQuery)
            .error(e.getMessage())
            .success(false)
            .build();
    }
    
    public String generateFallbackSQL(String naturalQuery, Schema schema) {
        // 生成简单的fallback SQL
        return "SELECT * FROM " + schema.getTables().get(0).getName() + " LIMIT 10";
    }
}
```
