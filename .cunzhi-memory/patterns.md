# 常用模式和最佳实践

- 已完成知识图谱在向量化模块和检索模块的深度集成，实现了实体向量化、关系推理、图谱增强检索等核心功能，形成稠密向量+稀疏向量+知识图谱的三重混合检索架构
- 检索模块已成功拆分为5个子模块：4-1-CoT推理与查询理解模块.md、4-2-查询重写与优化模块.md、4-3-混合检索引擎模块.md、4-4-智能重排与优化模块.md、4-5-自我反思与优化模块.md，原4-检索模块.md改为总览文档
- 已完成知识图谱与RAG系统的深度集成：1)新增4-6-知识图谱检索模块.md作为第6个检索子模块；2)在文档提取模块集成实体关系抽取功能；3)在智能分块模块集成实体感知分块策略；4)在向量化模块集成实体向量化功能；5)更新检索模块总览集成知识图谱检索流程；6)更新README.md文档结构，形成完整的稠密向量+稀疏向量+知识图谱三重混合检索架构
- 已完成删除所有文档中的性能指标部分：包括4-检索模块.md、4-6-知识图谱检索模块.md、README.md、0-RAG技术方案总览.md、4-4-智能重排与优化模块.md、4-1-CoT推理与查询理解模块.md、4-5-自我反思与优化模块.md、4-3-混合检索引擎模块.md、3-向量化模块.md、4-2-查询重写与优化模块.md等10个文档的性能指标部分
