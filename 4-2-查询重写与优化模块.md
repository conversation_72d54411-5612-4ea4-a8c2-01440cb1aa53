# 查询重写与优化模块

## 模块概述

查询重写与优化模块负责将用户的原始查询转换为多个优化的查询变体，通过查询扩展、改写、分解和多查询生成等技术，提高检索的召回率和准确性。该模块基于CoT推理结果进行智能优化，确保重写后的查询既保持原意又能更好地匹配文档内容。

## 核心功能

### 1. 查询扩展 (Query Expansion)
- **同义词扩展**：添加查询词的同义词和近义词
- **相关词扩展**：基于语义相关性添加相关词汇
- **概念扩展**：添加上位概念和下位概念
- **历史查询扩展**：基于用户历史查询进行扩展

### 2. 查询改写 (Query Reformulation)
- **语法改写**：调整查询的语法结构
- **语义改写**：保持语义的前提下改写表达方式
- **简化改写**：将复杂查询简化为多个简单查询
- **专业术语转换**：将口语化表达转换为专业术语

### 3. 查询分解 (Query Decomposition)
- **复合查询分解**：将复合查询分解为多个子查询
- **逻辑分解**：按照逻辑关系分解查询
- **时间分解**：按照时间维度分解查询
- **主题分解**：按照主题维度分解查询

### 4. 多查询生成 (Multi-Query Generation)
- **并行查询生成**：生成多个并行的查询变体
- **递进查询生成**：生成从宽泛到具体的查询序列
- **角度查询生成**：从不同角度生成查询
- **粒度查询生成**：生成不同粒度的查询

## 技术架构

### 1. 查询重写服务接口
```java
public interface QueryRewriteService {
    List<RewrittenQuery> rewriteQuery(SearchQuery originalQuery, QueryRewriteConfig config);
    RewrittenQuery expandQuery(SearchQuery query, QueryExpansionConfig config);
    RewrittenQuery reformulateQuery(SearchQuery query, QueryReformulationConfig config);
    List<RewrittenQuery> decomposeQuery(SearchQuery query, QueryDecompositionConfig config);
    List<RewrittenQuery> generateMultiQueries(SearchQuery query, MultiQueryConfig config);
}

@Data
@Builder
public class RewrittenQuery {
    private String originalQuery;
    private String rewrittenQuery;
    private QueryRewriteType type;
    private double confidence;
    private Map<String, Object> metadata;
    private List<String> addedTerms;
    private List<String> removedTerms;
    private long processingTime;
}

public enum QueryRewriteType {
    EXPANSION,           // 查询扩展
    REFORMULATION,       // 查询改写
    DECOMPOSITION,       // 查询分解
    MULTI_QUERY,         // 多查询生成
    SIMPLIFICATION,      // 查询简化
    SPECIALIZATION       // 查询专业化
}
```

### 2. 查询重写服务实现
```java
@Component
public class QueryRewriteServiceImpl implements QueryRewriteService {

    @Autowired
    private QueryExpansionService queryExpansionService;

    @Autowired
    private LLMService llmService;

    @Autowired
    private SynonymService synonymService;

    @Override
    public List<RewrittenQuery> rewriteQuery(SearchQuery originalQuery, QueryRewriteConfig config) {
        List<RewrittenQuery> rewrittenQueries = new ArrayList<>();

        // 1. 查询扩展
        if (config.isEnableExpansion()) {
            RewrittenQuery expandedQuery = expandQuery(originalQuery, config.getExpansionConfig());
            if (expandedQuery != null && expandedQuery.getConfidence() > config.getMinConfidence()) {
                rewrittenQueries.add(expandedQuery);
            }
        }

        // 2. 查询改写
        if (config.isEnableReformulation()) {
            RewrittenQuery reformulatedQuery = reformulateQuery(originalQuery, config.getReformulationConfig());
            if (reformulatedQuery != null && reformulatedQuery.getConfidence() > config.getMinConfidence()) {
                rewrittenQueries.add(reformulatedQuery);
            }
        }

        // 3. 查询分解
        if (config.isEnableDecomposition() && isComplexQuery(originalQuery.getText())) {
            List<RewrittenQuery> decomposedQueries = decomposeQuery(originalQuery, config.getDecompositionConfig());
            rewrittenQueries.addAll(decomposedQueries);
        }

        // 4. 多查询生成
        if (config.isEnableMultiQuery()) {
            List<RewrittenQuery> multiQueries = generateMultiQueries(originalQuery, config.getMultiQueryConfig());
            rewrittenQueries.addAll(multiQueries);
        }

        // 5. 查询去重和排序
        return deduplicateAndRank(rewrittenQueries, config);
    }

    @Override
    public RewrittenQuery expandQuery(SearchQuery query, QueryExpansionConfig config) {
        long startTime = System.currentTimeMillis();

        // 同义词扩展
        Set<String> synonyms = synonymService.getSynonyms(query.getText(), config.getLanguage());

        // 相关词扩展
        Set<String> relatedTerms = queryExpansionService.getRelatedTerms(query.getText(), config);

        // 构建扩展查询
        StringBuilder expandedQuery = new StringBuilder(query.getText());
        Set<String> addedTerms = new HashSet<>();

        // 添加同义词（权重较高）
        synonyms.stream()
            .limit(config.getMaxSynonyms())
            .forEach(synonym -> {
                expandedQuery.append(" ").append(synonym);
                addedTerms.add(synonym);
            });

        // 添加相关词（权重较低）
        relatedTerms.stream()
            .limit(config.getMaxRelatedTerms())
            .forEach(relatedTerm -> {
                expandedQuery.append(" ").append(relatedTerm);
                addedTerms.add(relatedTerm);
            });

        double confidence = calculateExpansionConfidence(addedTerms, query.getText(), config);

        return RewrittenQuery.builder()
            .originalQuery(query.getText())
            .rewrittenQuery(expandedQuery.toString().trim())
            .type(QueryRewriteType.EXPANSION)
            .confidence(confidence)
            .addedTerms(new ArrayList<>(addedTerms))
            .processingTime(System.currentTimeMillis() - startTime)
            .metadata(Map.of("synonymCount", synonyms.size(), "relatedTermCount", relatedTerms.size()))
            .build();
    }

    @Override
    public RewrittenQuery reformulateQuery(SearchQuery query, QueryReformulationConfig config) {
        long startTime = System.currentTimeMillis();

        // 构建改写提示词
        String prompt = buildReformulationPrompt(query.getText(), config);

        // 使用LLM进行查询改写
        String reformulatedQuery = llmService.generateText(prompt, config.getLlmConfig());

        // 清理和验证改写结果
        reformulatedQuery = cleanReformulatedQuery(reformulatedQuery);
        double confidence = validateReformulation(query.getText(), reformulatedQuery, config);

        if (confidence < config.getMinConfidence()) {
            return null; // 改写质量不够
        }

        return RewrittenQuery.builder()
            .originalQuery(query.getText())
            .rewrittenQuery(reformulatedQuery)
            .type(QueryRewriteType.REFORMULATION)
            .confidence(confidence)
            .processingTime(System.currentTimeMillis() - startTime)
            .metadata(Map.of("llmModel", config.getLlmConfig().getModelName()))
            .build();
    }

    @Override
    public List<RewrittenQuery> decomposeQuery(SearchQuery query, QueryDecompositionConfig config) {
        List<RewrittenQuery> decomposedQueries = new ArrayList<>();
        long startTime = System.currentTimeMillis();

        // 构建分解提示词
        String prompt = buildDecompositionPrompt(query.getText(), config);

        // 使用LLM进行查询分解
        String llmResponse = llmService.generateText(prompt, config.getLlmConfig());

        // 解析分解结果
        List<String> subQueries = parseDecompositionResponse(llmResponse);

        for (int i = 0; i < subQueries.size(); i++) {
            String subQuery = subQueries.get(i).trim();
            if (!subQuery.isEmpty() && !subQuery.equals(query.getText())) {
                double confidence = calculateDecompositionConfidence(subQuery, query.getText(), i, config);
                
                decomposedQueries.add(RewrittenQuery.builder()
                    .originalQuery(query.getText())
                    .rewrittenQuery(subQuery)
                    .type(QueryRewriteType.DECOMPOSITION)
                    .confidence(confidence)
                    .processingTime(System.currentTimeMillis() - startTime)
                    .metadata(Map.of("subQueryIndex", i, "totalSubQueries", subQueries.size()))
                    .build());
            }
        }

        return decomposedQueries;
    }

    @Override
    public List<RewrittenQuery> generateMultiQueries(SearchQuery query, MultiQueryConfig config) {
        List<RewrittenQuery> multiQueries = new ArrayList<>();
        long startTime = System.currentTimeMillis();

        // 构建多查询生成提示词
        String prompt = buildMultiQueryPrompt(query.getText(), config);

        // 使用LLM生成多个查询变体
        String llmResponse = llmService.generateText(prompt, config.getLlmConfig());

        // 解析多查询结果
        List<String> queryVariants = parseMultiQueryResponse(llmResponse);

        for (int i = 0; i < queryVariants.size() && i < config.getMaxQueries(); i++) {
            String variant = queryVariants.get(i).trim();
            if (!variant.isEmpty() && !variant.equals(query.getText())) {
                double confidence = calculateMultiQueryConfidence(variant, query.getText(), config);
                
                multiQueries.add(RewrittenQuery.builder()
                    .originalQuery(query.getText())
                    .rewrittenQuery(variant)
                    .type(QueryRewriteType.MULTI_QUERY)
                    .confidence(confidence)
                    .processingTime(System.currentTimeMillis() - startTime)
                    .metadata(Map.of("variantIndex", i, "generationMethod", config.getGenerationMethod()))
                    .build());
            }
        }

        return multiQueries;
    }

    private String buildReformulationPrompt(String originalQuery, QueryReformulationConfig config) {
        return String.format(
            "请将以下查询改写为更清晰、更准确的表达方式，保持原意不变：\n" +
            "原查询：%s\n" +
            "改写要求：%s\n" +
            "请只返回改写后的查询，不要包含其他内容。",
            originalQuery,
            config.getReformulationInstructions()
        );
    }

    private String buildDecompositionPrompt(String originalQuery, QueryDecompositionConfig config) {
        return String.format(
            "请将以下复合查询分解为多个简单的子查询：\n" +
            "原查询：%s\n" +
            "分解要求：\n" +
            "1. 每个子查询应该是独立的、完整的\n" +
            "2. 子查询的组合应该覆盖原查询的全部意图\n" +
            "3. 最多分解为%d个子查询\n" +
            "请按行返回子查询，每行一个。",
            originalQuery,
            config.getMaxSubQueries()
        );
    }

    private String buildMultiQueryPrompt(String originalQuery, MultiQueryConfig config) {
        return String.format(
            "请为以下查询生成%d个不同的表达方式，保持相同的搜索意图：\n" +
            "原查询：%s\n" +
            "生成要求：\n" +
            "1. 每个变体应该从不同角度表达相同的搜索需求\n" +
            "2. 使用不同的词汇和句式结构\n" +
            "3. 保持查询的核心语义不变\n" +
            "请按行返回查询变体，每行一个。",
            config.getMaxQueries(),
            originalQuery
        );
    }

    private boolean isComplexQuery(String query) {
        // 判断查询是否复杂（包含多个概念、连接词等）
        String[] complexIndicators = {"和", "或", "以及", "关于", "如何", "什么", "为什么"};
        return Arrays.stream(complexIndicators).anyMatch(query::contains) ||
               query.split("\\s+").length > 5;
    }

    private List<RewrittenQuery> deduplicateAndRank(List<RewrittenQuery> queries, QueryRewriteConfig config) {
        // 去重
        Map<String, RewrittenQuery> uniqueQueries = new LinkedHashMap<>();
        for (RewrittenQuery query : queries) {
            String normalizedQuery = normalizeQuery(query.getRewrittenQuery());
            if (!uniqueQueries.containsKey(normalizedQuery) || 
                query.getConfidence() > uniqueQueries.get(normalizedQuery).getConfidence()) {
                uniqueQueries.put(normalizedQuery, query);
            }
        }

        // 排序（按置信度降序）
        return uniqueQueries.values().stream()
            .sorted((q1, q2) -> Double.compare(q2.getConfidence(), q1.getConfidence()))
            .limit(config.getMaxRewrittenQueries())
            .collect(Collectors.toList());
    }
}
```

## 流程图

```mermaid
graph TD
    A[接收原始查询] --> B[查询复杂度分析]
    B --> C{选择重写策略}
    
    C -->|简单查询| D[查询扩展]
    C -->|中等复杂| E[查询改写]
    C -->|复杂查询| F[查询分解]
    C -->|多角度需求| G[多查询生成]
    
    D --> H[同义词扩展]
    D --> I[相关词扩展]
    
    E --> J[语法改写]
    E --> K[语义改写]
    
    F --> L[逻辑分解]
    F --> M[主题分解]
    
    G --> N[并行查询生成]
    G --> O[角度查询生成]
    
    H --> P[查询去重排序]
    I --> P
    J --> P
    K --> P
    L --> P
    M --> P
    N --> P
    O --> P
    
    P --> Q[输出重写查询列表]
    Q --> R[传递给混合检索引擎]

    style A fill:#e1f5fe
    style Q fill:#c8e6c9
    style R fill:#fff3e0
```

## 配置参数

### 查询重写配置
```yaml
query_rewrite:
  enabled: true
  max_rewritten_queries: 5
  min_confidence: 0.6
  
  expansion:
    enabled: true
    max_synonyms: 3
    max_related_terms: 2
    language: "zh"
    
  reformulation:
    enabled: true
    llm_model: "gpt-3.5-turbo"
    max_tokens: 200
    temperature: 0.5
    
  decomposition:
    enabled: true
    max_sub_queries: 3
    min_complexity_threshold: 0.7
    
  multi_query:
    enabled: true
    max_queries: 3
    generation_method: "diverse"
```
